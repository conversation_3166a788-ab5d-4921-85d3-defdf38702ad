package com.yancao.qrscanner.camera

import android.graphics.Bitmap
import android.util.Log
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage


/**
 *
 */
class QRCodeAnalyzer {

    private val options = BarcodeScannerOptions.Builder()
        .setBarcodeFormats(Barcode.FORMAT_QR_CODE)
        .build()

    private val scanner = BarcodeScanning.getClient(options)

    /**
     * 异步分析给定的 Bitmap 图像以查找二维码。
     * 使用高阶函数作为回调，使调用更简洁。
     *
     * @param bitmap 你想要分析的图像 Bitmap。
     * @param onSuccess 成功时调用的 Lambda，接收一个条码列表。
     * @param onError 失败时调用的 Lambda，接收一个异常。
     */
    fun analyze(
        bitmap: Bitmap,
        onSuccess: (qrCodes: List<Barcode>) -> Unit,
        onError: (exception: Exception) -> Unit
    ) {
        val image = InputImage.fromBitmap(bitmap, 0)
        scanner.process(image)
            .addOnSuccessListener { barcodes ->
                onSuccess(barcodes)
            }
            .addOnFailureListener { e ->
                Log.e("QRCodeAnalyzer", "二维码识别失败", e)
                onError(e)
            }
    }
}

/**
 * 调用方法：
 * class MainActivity : AppCompatActivity() {
 *
 *     private val qrCodeAnalyzer = QRCodeAnalyzer()
 *
 *     override fun onCreate(savedInstanceState: Bundle?) {
 *         super.onCreate(savedInstanceState)
 *         setContentView(R.layout.activity_main)
 *
 *         val myBitmap: Bitmap? = BitmapFactory.decodeResource(resources, R.drawable.sample_with_qr_codes)
 *
 *         myBitmap?.let { bitmap ->
 *             // 调用现在变得非常干净和直观
 *             qrCodeAnalyzer.analyze(
 *                 bitmap,
 *                 onSuccess = { qrCodes ->
 *                     if (qrCodes.isEmpty()) {
 *                         showToast("在图像中未找到任何二维码。")
 *                     } else {
 *                         Log.d("QRCodeResult", "成功识别到 ${qrCodes.size} 个二维码:")
 *                         val resultsText = qrCodes.mapIndexed { index, barcode ->
 *                             "二维码 ${index + 1}: ${barcode.rawValue}"
 *                         }.joinToString("\n")
 *                         showToast(resultsText, isLong = true)
 *                     }
 *                 },
 *                 onError = { exception ->
 *                     showToast("识别失败: ${exception.message}")
 *                 }
 *             )
 *         } ?: showToast("无法加载图像")
 *     }
 *
 *     // 将 UI 操作封装成一个辅助函数
 *     private fun showToast(message: String, isLong: Boolean = false) {
 *         Toast.makeText(this, message, if (isLong) Toast.LENGTH_LONG else Toast.LENGTH_SHORT).show()
 *     }
 * }
 */