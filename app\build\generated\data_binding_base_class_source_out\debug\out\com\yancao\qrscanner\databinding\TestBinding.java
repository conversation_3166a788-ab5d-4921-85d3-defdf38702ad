// Generated by view binder compiler. Do not edit!
package com.yancao.qrscanner.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.yancao.qrscanner.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class TestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnDecreaseExposure;

  @NonNull
  public final Button btnIncreaseExposure;

  @NonNull
  public final Button btnLowLightMode;

  @NonNull
  public final Button btnOptimizeForScanning;

  @NonNull
  public final Button btnResetExposure;

  @NonNull
  public final Button btnToggleExposurePanel;

  @NonNull
  public final LinearLayout exposureControlPanel;

  @NonNull
  public final SeekBar seekBarExposure;

  @NonNull
  public final Switch switchExposureControl;

  @NonNull
  public final TextView tvExposureValue;

  private TestBinding(@NonNull LinearLayout rootView, @NonNull Button btnDecreaseExposure,
      @NonNull Button btnIncreaseExposure, @NonNull Button btnLowLightMode,
      @NonNull Button btnOptimizeForScanning, @NonNull Button btnResetExposure,
      @NonNull Button btnToggleExposurePanel, @NonNull LinearLayout exposureControlPanel,
      @NonNull SeekBar seekBarExposure, @NonNull Switch switchExposureControl,
      @NonNull TextView tvExposureValue) {
    this.rootView = rootView;
    this.btnDecreaseExposure = btnDecreaseExposure;
    this.btnIncreaseExposure = btnIncreaseExposure;
    this.btnLowLightMode = btnLowLightMode;
    this.btnOptimizeForScanning = btnOptimizeForScanning;
    this.btnResetExposure = btnResetExposure;
    this.btnToggleExposurePanel = btnToggleExposurePanel;
    this.exposureControlPanel = exposureControlPanel;
    this.seekBarExposure = seekBarExposure;
    this.switchExposureControl = switchExposureControl;
    this.tvExposureValue = tvExposureValue;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static TestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static TestBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static TestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_decrease_exposure;
      Button btnDecreaseExposure = ViewBindings.findChildViewById(rootView, id);
      if (btnDecreaseExposure == null) {
        break missingId;
      }

      id = R.id.btn_increase_exposure;
      Button btnIncreaseExposure = ViewBindings.findChildViewById(rootView, id);
      if (btnIncreaseExposure == null) {
        break missingId;
      }

      id = R.id.btn_low_light_mode;
      Button btnLowLightMode = ViewBindings.findChildViewById(rootView, id);
      if (btnLowLightMode == null) {
        break missingId;
      }

      id = R.id.btn_optimize_for_scanning;
      Button btnOptimizeForScanning = ViewBindings.findChildViewById(rootView, id);
      if (btnOptimizeForScanning == null) {
        break missingId;
      }

      id = R.id.btn_reset_exposure;
      Button btnResetExposure = ViewBindings.findChildViewById(rootView, id);
      if (btnResetExposure == null) {
        break missingId;
      }

      id = R.id.btn_toggle_exposure_panel;
      Button btnToggleExposurePanel = ViewBindings.findChildViewById(rootView, id);
      if (btnToggleExposurePanel == null) {
        break missingId;
      }

      id = R.id.exposure_control_panel;
      LinearLayout exposureControlPanel = ViewBindings.findChildViewById(rootView, id);
      if (exposureControlPanel == null) {
        break missingId;
      }

      id = R.id.seek_bar_exposure;
      SeekBar seekBarExposure = ViewBindings.findChildViewById(rootView, id);
      if (seekBarExposure == null) {
        break missingId;
      }

      id = R.id.switch_exposure_control;
      Switch switchExposureControl = ViewBindings.findChildViewById(rootView, id);
      if (switchExposureControl == null) {
        break missingId;
      }

      id = R.id.tv_exposure_value;
      TextView tvExposureValue = ViewBindings.findChildViewById(rootView, id);
      if (tvExposureValue == null) {
        break missingId;
      }

      return new TestBinding((LinearLayout) rootView, btnDecreaseExposure, btnIncreaseExposure,
          btnLowLightMode, btnOptimizeForScanning, btnResetExposure, btnToggleExposurePanel,
          exposureControlPanel, seekBarExposure, switchExposureControl, tvExposureValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
