<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="test" modulePackage="com.yancao.qrscanner" filePath="app\src\main\res\layout\test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/test_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="194" endOffset="14"/></Target><Target id="@+id/switch_exposure_control" view="Switch"><Expressions/><location startLine="32" startOffset="8" endLine="38" endOffset="61"/></Target><Target id="@+id/tv_exposure_value" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="70" endOffset="38"/></Target><Target id="@+id/exposure_control_panel" view="LinearLayout"><Expressions/><location startLine="74" startOffset="4" endLine="181" endOffset="18"/></Target><Target id="@+id/btn_decrease_exposure" view="Button"><Expressions/><location startLine="90" startOffset="12" endLine="101" endOffset="41"/></Target><Target id="@+id/btn_reset_exposure" view="Button"><Expressions/><location startLine="103" startOffset="12" endLine="112" endOffset="41"/></Target><Target id="@+id/btn_increase_exposure" view="Button"><Expressions/><location startLine="114" startOffset="12" endLine="125" endOffset="41"/></Target><Target id="@+id/seek_bar_exposure" view="SeekBar"><Expressions/><location startLine="144" startOffset="12" endLine="149" endOffset="58"/></Target><Target id="@+id/btn_optimize_for_scanning" view="Button"><Expressions/><location startLine="159" startOffset="12" endLine="168" endOffset="40"/></Target><Target id="@+id/btn_low_light_mode" view="Button"><Expressions/><location startLine="170" startOffset="12" endLine="179" endOffset="40"/></Target><Target id="@+id/btn_toggle_exposure_panel" view="Button"><Expressions/><location startLine="184" startOffset="4" endLine="192" endOffset="32"/></Target></Targets></Layout>