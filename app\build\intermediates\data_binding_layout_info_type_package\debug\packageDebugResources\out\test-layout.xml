<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="test" modulePackage="com.yancao.qrscanner" filePath="app\src\main\res\layout\test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/test_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="203" endOffset="51"/></Target><Target id="@+id/preview_view" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="51"/></Target><Target id="@+id/qr_overlay_view" view="com.yancao.qrscanner.ui.QrCodeOverlayView"><Expressions/><location startLine="19" startOffset="4" endLine="26" endOffset="62"/></Target><Target id="@+id/tv_scan_results" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="40" endOffset="43"/></Target><Target id="@+id/tv_lens_type" view="TextView"><Expressions/><location startLine="43" startOffset="4" endLine="56" endOffset="35"/></Target><Target id="@+id/btn_flashlight" view="Button"><Expressions/><location startLine="59" startOffset="4" endLine="71" endOffset="34"/></Target><Target id="@+id/zoom_control_panel" view="FrameLayout"><Expressions/><location startLine="74" startOffset="4" endLine="155" endOffset="17"/></Target><Target id="@+id/zoom_slider_container" view="LinearLayout"><Expressions/><location startLine="84" startOffset="8" endLine="153" endOffset="22"/></Target><Target id="@+id/tv_zoom_ratio" view="TextView"><Expressions/><location startLine="95" startOffset="12" endLine="104" endOffset="51"/></Target><Target id="@+id/seek_bar_zoom" view="SeekBar"><Expressions/><location startLine="108" startOffset="12" endLine="115" endOffset="58"/></Target><Target id="@+id/control_panel" view="LinearLayout"><Expressions/><location startLine="158" startOffset="4" endLine="201" endOffset="18"/></Target><Target id="@+id/button_container" view="LinearLayout"><Expressions/><location startLine="170" startOffset="8" endLine="199" endOffset="22"/></Target><Target id="@+id/btn_scan" view="Button"><Expressions/><location startLine="178" startOffset="12" endLine="186" endOffset="58"/></Target><Target id="@+id/btn_broadcast" view="Button"><Expressions/><location startLine="189" startOffset="12" endLine="197" endOffset="58"/></Target></Targets></Layout>