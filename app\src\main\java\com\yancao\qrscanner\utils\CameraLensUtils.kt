package com.yancao.qrscanner.utils

import android.content.Context
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.util.Log
import androidx.camera.camera2.interop.Camera2CameraInfo
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.CameraInfo
import androidx.camera.core.CameraSelector
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import java.util.concurrent.ExecutionException

/**
 * 相机镜头工具类
 *
 * 功能说明：
 * 1. 检测设备支持的镜头类型（超广角、主摄、长焦）
 * 2. 根据物理ID选择合适的镜头
 * 3. 提供镜头切换逻辑
 * 4. 管理不同镜头的缩放范围
 *
 * 设计原理：
 * - 使用Camera2 API获取物理镜头信息
 * - 通过焦距范围判断镜头类型
 * - 超广角：焦距 < 3.0mm
 * - 主摄：焦距 3.0-7.0mm
 * - 长焦：焦距 > 7.0mm
 */
class CameraLensUtils(private val context: Context) {

    companion object {
        private const val TAG = "CameraLensUtils"

        // 镜头类型定义
        const val LENS_TYPE_ULTRA_WIDE = 0
        const val LENS_TYPE_MAIN = 1
        const val LENS_TYPE_TELEPHOTO = 2

        // 焦距范围定义（毫米）
        private const val ULTRA_WIDE_MAX_FOCAL_LENGTH = 3.0f
        private const val MAIN_MAX_FOCAL_LENGTH = 7.0f
    }

    // 镜头信息数据类
    data class LensInfo(
        val physicalId: String,
        val lensType: Int,
        val focalLength: Float,
        val minZoomRatio: Float,
        val maxZoomRatio: Float,
        val cameraSelector: CameraSelector
    )

    private val camera2Manager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
    private var availableLenses = mutableListOf<LensInfo>()
    private var currentLensType = LENS_TYPE_MAIN

    /**
     * 初始化镜头信息
     * 扫描所有可用镜头并分类
     */
    @OptIn(ExperimentalCamera2Interop::class)
    fun initializeLenses(): Boolean {
        return try {
            availableLenses.clear()

            // 获取所有相机ID
            val cameraIds = camera2Manager.cameraIdList
            Log.d(TAG, "发现 ${cameraIds.size} 个相机")

            for (cameraId in cameraIds) {
                val characteristics = camera2Manager.getCameraCharacteristics(cameraId)
                val lensFacing = characteristics.get(CameraCharacteristics.LENS_FACING)

                // 只处理后置摄像头
                if (lensFacing == CameraCharacteristics.LENS_FACING_BACK) {
                    analyzeLens(cameraId, characteristics)
                }
            }

            // 按镜头类型排序：超广角 -> 主摄 -> 长焦
            availableLenses.sortBy { it.lensType }

            Log.d(TAG, "初始化完成，发现 ${availableLenses.size} 个后置镜头")
            availableLenses.forEach { lens ->
                Log.d(TAG, "镜头: ${getLensTypeName(lens.lensType)}, 焦距: ${lens.focalLength}mm, 缩放: ${lens.minZoomRatio}-${lens.maxZoomRatio}")
            }

            availableLenses.isNotEmpty()
        } catch (e: Exception) {
            Log.e(TAG, "初始化镜头失败", e)
            false
        }
    }

    /**
     * 分析单个镜头特性
     */
    private fun analyzeLens(cameraId: String, characteristics: CameraCharacteristics) {
        try {
            // 获取焦距信息
            val focalLengths = characteristics.get(CameraCharacteristics.LENS_INFO_AVAILABLE_FOCAL_LENGTHS)
            val focalLength = focalLengths?.firstOrNull() ?: 0f

            // 获取缩放范围
            val maxZoom = characteristics.get(CameraCharacteristics.SCALER_AVAILABLE_MAX_DIGITAL_ZOOM) ?: 1.0f
            val minZoom = 1.0f

            // 根据焦距判断镜头类型
            val lensType = when {
                focalLength < ULTRA_WIDE_MAX_FOCAL_LENGTH -> LENS_TYPE_ULTRA_WIDE
                focalLength <= MAIN_MAX_FOCAL_LENGTH -> LENS_TYPE_MAIN
                else -> LENS_TYPE_TELEPHOTO
            }

            // 创建对应的CameraSelector
            val cameraSelector = when (lensType) {
                LENS_TYPE_ULTRA_WIDE -> CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                    .build()
                LENS_TYPE_MAIN -> CameraSelector.DEFAULT_BACK_CAMERA
                else -> CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                    .build()
            }

            val lensInfo = LensInfo(
                physicalId = cameraId,
                lensType = lensType,
                focalLength = focalLength,
                minZoomRatio = minZoom,
                maxZoomRatio = maxZoom,
                cameraSelector = cameraSelector
            )

            availableLenses.add(lensInfo)

        } catch (e: Exception) {
            Log.e(TAG, "分析镜头 $cameraId 失败", e)
        }
    }

    /**
     * 获取当前推荐的镜头选择器
     * 根据缩放比例自动选择最合适的镜头
     */
    fun getRecommendedCameraSelector(targetZoomRatio: Float): CameraSelector {
        if (availableLenses.isEmpty()) {
            return CameraSelector.DEFAULT_BACK_CAMERA
        }

        // 缩放逻辑：
        // 0.5x-0.9x: 优先使用超广角
        // 1.0x-2.0x: 使用主摄
        // 2.0x以上: 如果有长焦则使用长焦，否则用主摄数字变焦

        val recommendedLens = when {
            targetZoomRatio < 1.0f -> {
                // 寻找超广角镜头
                availableLenses.find { it.lensType == LENS_TYPE_ULTRA_WIDE }
                    ?: availableLenses.find { it.lensType == LENS_TYPE_MAIN }
                    ?: availableLenses.first()
            }
            targetZoomRatio <= 2.0f -> {
                // 使用主摄
                availableLenses.find { it.lensType == LENS_TYPE_MAIN }
                    ?: availableLenses.first()
            }
            else -> {
                // 优先长焦，其次主摄
                availableLenses.find { it.lensType == LENS_TYPE_TELEPHOTO }
                    ?: availableLenses.find { it.lensType == LENS_TYPE_MAIN }
                    ?: availableLenses.first()
            }
        }

        currentLensType = recommendedLens.lensType
        Log.d(TAG, "缩放 ${targetZoomRatio}x 推荐使用: ${getLensTypeName(recommendedLens.lensType)}")

        return recommendedLens.cameraSelector
    }

    /**
     * 获取指定镜头类型的选择器
     */
    fun getCameraSelectorByType(lensType: Int): CameraSelector? {
        return availableLenses.find { it.lensType == lensType }?.cameraSelector
    }

    /**
     * 检查是否支持超广角镜头
     */
    fun hasUltraWideLens(): Boolean {
        return availableLenses.any { it.lensType == LENS_TYPE_ULTRA_WIDE }
    }

    /**
     * 检查是否支持长焦镜头
     */
    fun hasTelephotoLens(): Boolean {
        return availableLenses.any { it.lensType == LENS_TYPE_TELEPHOTO }
    }

    /**
     * 获取当前镜头类型
     */
    fun getCurrentLensType(): Int {
        return currentLensType
    }

    /**
     * 获取全局缩放范围（考虑所有镜头）
     * 返回 Pair(最小缩放, 最大缩放)
     */
    fun getGlobalZoomRange(): Pair<Float, Float> {
        if (availableLenses.isEmpty()) {
            return Pair(0.5f, 10.0f) // 默认范围
        }

        // 超广角可以提供0.5x效果，主摄和长焦提供更高倍数
        val minZoom = if (hasUltraWideLens()) 0.5f else 1.0f
        val maxZoom = availableLenses.maxOfOrNull { it.maxZoomRatio } ?: 10.0f

        return Pair(minZoom, maxZoom.coerceAtMost(10.0f)) // 限制最大10x
    }

    /**
     * 将全局缩放比例转换为当前镜头的实际缩放比例
     */
    fun convertToActualZoomRatio(globalZoomRatio: Float): Float {
        return when {
            globalZoomRatio < 1.0f && hasUltraWideLens() -> {
                // 超广角镜头：0.5x-0.9x 映射到 1.0x-1.8x
                1.0f + (globalZoomRatio - 0.5f) * 1.6f
            }
            else -> {
                // 主摄或长焦：直接使用全局缩放值
                globalZoomRatio
            }
        }
    }

    /**
     * 获取镜头类型名称（用于日志）
     */
    private fun getLensTypeName(lensType: Int): String {
        return when (lensType) {
            LENS_TYPE_ULTRA_WIDE -> "超广角"
            LENS_TYPE_MAIN -> "主摄"
            LENS_TYPE_TELEPHOTO -> "长焦"
            else -> "未知"
        }
    }

    /**
     * 获取所有可用镜头信息
     */
    fun getAvailableLenses(): List<LensInfo> {
        return availableLenses.toList()
    }
}