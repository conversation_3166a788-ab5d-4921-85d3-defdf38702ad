// Generated by view binder compiler. Do not edit!
package com.yancao.qrscanner.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.yancao.qrscanner.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityResultsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button btnQrAnalyzer;

  @NonNull
  public final ImageView ivCapturedImage;

  @NonNull
  public final TextView resultsDisplay;

  private ActivityResultsBinding(@NonNull FrameLayout rootView, @NonNull Button btnQrAnalyzer,
      @NonNull ImageView ivCapturedImage, @NonNull TextView resultsDisplay) {
    this.rootView = rootView;
    this.btnQrAnalyzer = btnQrAnalyzer;
    this.ivCapturedImage = ivCapturedImage;
    this.resultsDisplay = resultsDisplay;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityResultsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityResultsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_results, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityResultsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_qr_analyzer;
      Button btnQrAnalyzer = ViewBindings.findChildViewById(rootView, id);
      if (btnQrAnalyzer == null) {
        break missingId;
      }

      id = R.id.iv_captured_image;
      ImageView ivCapturedImage = ViewBindings.findChildViewById(rootView, id);
      if (ivCapturedImage == null) {
        break missingId;
      }

      id = R.id.results_display;
      TextView resultsDisplay = ViewBindings.findChildViewById(rootView, id);
      if (resultsDisplay == null) {
        break missingId;
      }

      return new ActivityResultsBinding((FrameLayout) rootView, btnQrAnalyzer, ivCapturedImage,
          resultsDisplay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
