R_DEF: Internal format may change without notice
local
color black
color button_accent
color button_primary
color button_secondary
color button_tertiary
color exposure_bright
color exposure_control_bg
color exposure_dark
color exposure_normal
color semi_transparent_black
color text_primary_light
color text_secondary_light
color white
drawable circle_button_selected
drawable circle_button_unselected
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable red_border_background
drawable zoom_button_selector
id btn_broadcast
id btn_flashlight
id btn_qr_analyzer
id btn_scan
id button_container
id control_panel
id iv_captured_image
id preview_view
id qr_overlay_view
id results_display
id seek_bar_zoom
id tv_lens_type
id tv_scan_results
id tv_zoom_ratio
id zoom_control_panel
id zoom_slider_container
layout activity_main
layout activity_results
layout test
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string decrease_exposure
string exposure_control
string exposure_decreased
string exposure_increased
string exposure_max_reached
string exposure_min_reached
string exposure_not_supported
string exposure_optimized
string exposure_reset
string exposure_value
string hide_detailed_control
string increase_exposure
string low_light_mode
string optimize_for_scanning
string reset_exposure
string show_detailed_control
style Base.Theme.QRscanner
style ExposureControlButton
style ExposureControlText
style ExposureControlTitle
style Theme.QRscanner
xml backup_rules
xml data_extraction_rules
