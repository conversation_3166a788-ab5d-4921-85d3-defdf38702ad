1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.yancao.qrscanner"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:5:5-65
11-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:5:22-62
12
13    <uses-feature android:name="android.hardware.camera.any" />
13-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:6:5-64
13-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:6:19-61
14
15    <permission
15-->[androidx.core:core:1.13.0] D:\data\AndroidGradle\caches\8.11.1\transforms\f146977ca897dc8aef412974ba039f90\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.yancao.qrscanner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.13.0] D:\data\AndroidGradle\caches\8.11.1\transforms\f146977ca897dc8aef412974ba039f90\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.13.0] D:\data\AndroidGradle\caches\8.11.1\transforms\f146977ca897dc8aef412974ba039f90\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.yancao.qrscanner.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
19-->[androidx.core:core:1.13.0] D:\data\AndroidGradle\caches\8.11.1\transforms\f146977ca897dc8aef412974ba039f90\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.13.0] D:\data\AndroidGradle\caches\8.11.1\transforms\f146977ca897dc8aef412974ba039f90\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
20    <!-- <uses-sdk android:minSdkVersion="14"/> -->
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
21-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
22    <uses-permission android:name="android.permission.INTERNET" />
22-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
22-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
23
24    <application
24-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:8:5-27:19
25        android:allowBackup="true"
25-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:9:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.13.0] D:\data\AndroidGradle\caches\8.11.1\transforms\f146977ca897dc8aef412974ba039f90\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:10:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:fullBackupContent="@xml/backup_rules"
30-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:11:9-54
31        android:icon="@mipmap/ic_launcher"
31-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:12:9-43
32        android:label="@string/app_name"
32-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:13:9-41
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:14:9-54
34        android:supportsRtl="true"
34-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:15:9-35
35        android:theme="@style/Theme.QRscanner" >
35-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:16:9-47
36        <activity
36-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:18:9-26:20
37            android:name="com.yancao.qrscanner.ui.QrScanActivity"
37-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:19:13-46
38            android:exported="true" >
38-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:20:13-36
39            <intent-filter>
39-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:21:13-25:29
40                <action android:name="android.intent.action.MAIN" />
40-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:22:17-69
40-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:22:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:24:17-77
42-->D:\data\AndroidProgram\project\QRscanner\app\src\main\AndroidManifest.xml:24:27-74
43            </intent-filter>
44        </activity>
45
46        <service
46-->[androidx.camera:camera-camera2:1.4.2] D:\data\AndroidGradle\caches\8.11.1\transforms\cab0d23975582126b5662770641446ed\transformed\camera-camera2-1.4.2\AndroidManifest.xml:24:9-33:19
47            android:name="androidx.camera.core.impl.MetadataHolderService"
47-->[androidx.camera:camera-camera2:1.4.2] D:\data\AndroidGradle\caches\8.11.1\transforms\cab0d23975582126b5662770641446ed\transformed\camera-camera2-1.4.2\AndroidManifest.xml:25:13-75
48            android:enabled="false"
48-->[androidx.camera:camera-camera2:1.4.2] D:\data\AndroidGradle\caches\8.11.1\transforms\cab0d23975582126b5662770641446ed\transformed\camera-camera2-1.4.2\AndroidManifest.xml:26:13-36
49            android:exported="false" >
49-->[androidx.camera:camera-camera2:1.4.2] D:\data\AndroidGradle\caches\8.11.1\transforms\cab0d23975582126b5662770641446ed\transformed\camera-camera2-1.4.2\AndroidManifest.xml:27:13-37
50            <meta-data
50-->[androidx.camera:camera-camera2:1.4.2] D:\data\AndroidGradle\caches\8.11.1\transforms\cab0d23975582126b5662770641446ed\transformed\camera-camera2-1.4.2\AndroidManifest.xml:30:13-32:89
51                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
51-->[androidx.camera:camera-camera2:1.4.2] D:\data\AndroidGradle\caches\8.11.1\transforms\cab0d23975582126b5662770641446ed\transformed\camera-camera2-1.4.2\AndroidManifest.xml:31:17-103
52                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
52-->[androidx.camera:camera-camera2:1.4.2] D:\data\AndroidGradle\caches\8.11.1\transforms\cab0d23975582126b5662770641446ed\transformed\camera-camera2-1.4.2\AndroidManifest.xml:32:17-86
53        </service>
54        <service
54-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\ede4b13a8fa776d57adda41e1fcfe1b4\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
55            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
55-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\ede4b13a8fa776d57adda41e1fcfe1b4\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
56            android:directBootAware="true"
56-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
57            android:exported="false" >
57-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\ede4b13a8fa776d57adda41e1fcfe1b4\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
58            <meta-data
58-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\ede4b13a8fa776d57adda41e1fcfe1b4\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
59                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
59-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\ede4b13a8fa776d57adda41e1fcfe1b4\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
60                android:value="com.google.firebase.components.ComponentRegistrar" />
60-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\ede4b13a8fa776d57adda41e1fcfe1b4\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
61            <meta-data
61-->[com.google.mlkit:vision-common:17.3.0] D:\data\AndroidGradle\caches\8.11.1\transforms\dd892170ad6358c0d9cd3c47f9754d08\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
62                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
62-->[com.google.mlkit:vision-common:17.3.0] D:\data\AndroidGradle\caches\8.11.1\transforms\dd892170ad6358c0d9cd3c47f9754d08\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
63                android:value="com.google.firebase.components.ComponentRegistrar" />
63-->[com.google.mlkit:vision-common:17.3.0] D:\data\AndroidGradle\caches\8.11.1\transforms\dd892170ad6358c0d9cd3c47f9754d08\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
64            <meta-data
64-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
65                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
65-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
66                android:value="com.google.firebase.components.ComponentRegistrar" />
66-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
67        </service>
68
69        <provider
69-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
70            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
70-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
71            android:authorities="com.yancao.qrscanner.mlkitinitprovider"
71-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
72            android:exported="false"
72-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
73            android:initOrder="99" />
73-->[com.google.mlkit:common:18.11.0] D:\data\AndroidGradle\caches\8.11.1\transforms\74436747d5934fafebdb0e0fe8e777ff\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
74
75        <activity
75-->[com.google.android.gms:play-services-base:18.5.0] D:\data\AndroidGradle\caches\8.11.1\transforms\5753efefcaa7b6ff3a6b297f30c575dc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
76            android:name="com.google.android.gms.common.api.GoogleApiActivity"
76-->[com.google.android.gms:play-services-base:18.5.0] D:\data\AndroidGradle\caches\8.11.1\transforms\5753efefcaa7b6ff3a6b297f30c575dc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
77            android:exported="false"
77-->[com.google.android.gms:play-services-base:18.5.0] D:\data\AndroidGradle\caches\8.11.1\transforms\5753efefcaa7b6ff3a6b297f30c575dc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
78            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
78-->[com.google.android.gms:play-services-base:18.5.0] D:\data\AndroidGradle\caches\8.11.1\transforms\5753efefcaa7b6ff3a6b297f30c575dc\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
79
80        <meta-data
80-->[com.google.android.gms:play-services-basement:18.4.0] D:\data\AndroidGradle\caches\8.11.1\transforms\bddc079709e6c2a87130e68b6b17a30e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
81            android:name="com.google.android.gms.version"
81-->[com.google.android.gms:play-services-basement:18.4.0] D:\data\AndroidGradle\caches\8.11.1\transforms\bddc079709e6c2a87130e68b6b17a30e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
82            android:value="@integer/google_play_services_version" />
82-->[com.google.android.gms:play-services-basement:18.4.0] D:\data\AndroidGradle\caches\8.11.1\transforms\bddc079709e6c2a87130e68b6b17a30e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
83
84        <provider
84-->[androidx.emoji2:emoji2:1.2.0] D:\data\AndroidGradle\caches\8.11.1\transforms\56d63cccac39b1387d07658cdd9bde82\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
85            android:name="androidx.startup.InitializationProvider"
85-->[androidx.emoji2:emoji2:1.2.0] D:\data\AndroidGradle\caches\8.11.1\transforms\56d63cccac39b1387d07658cdd9bde82\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
86            android:authorities="com.yancao.qrscanner.androidx-startup"
86-->[androidx.emoji2:emoji2:1.2.0] D:\data\AndroidGradle\caches\8.11.1\transforms\56d63cccac39b1387d07658cdd9bde82\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
87            android:exported="false" >
87-->[androidx.emoji2:emoji2:1.2.0] D:\data\AndroidGradle\caches\8.11.1\transforms\56d63cccac39b1387d07658cdd9bde82\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
88            <meta-data
88-->[androidx.emoji2:emoji2:1.2.0] D:\data\AndroidGradle\caches\8.11.1\transforms\56d63cccac39b1387d07658cdd9bde82\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.emoji2.text.EmojiCompatInitializer"
89-->[androidx.emoji2:emoji2:1.2.0] D:\data\AndroidGradle\caches\8.11.1\transforms\56d63cccac39b1387d07658cdd9bde82\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
90                android:value="androidx.startup" />
90-->[androidx.emoji2:emoji2:1.2.0] D:\data\AndroidGradle\caches\8.11.1\transforms\56d63cccac39b1387d07658cdd9bde82\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
91            <meta-data
91-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\data\AndroidGradle\caches\8.11.1\transforms\68c739049ad7c2d860da7e8406c8a9cc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
92-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\data\AndroidGradle\caches\8.11.1\transforms\68c739049ad7c2d860da7e8406c8a9cc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
93                android:value="androidx.startup" />
93-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\data\AndroidGradle\caches\8.11.1\transforms\68c739049ad7c2d860da7e8406c8a9cc\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
94            <meta-data
94-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
96                android:value="androidx.startup" />
96-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
97        </provider>
98
99        <receiver
99-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
100            android:name="androidx.profileinstaller.ProfileInstallReceiver"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
101            android:directBootAware="false"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
102            android:enabled="true"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
103            android:exported="true"
103-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
104            android:permission="android.permission.DUMP" >
104-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
106                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
106-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
109                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
109-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
112                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
112-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
113            </intent-filter>
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
115                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
115-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
115-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\data\AndroidGradle\caches\8.11.1\transforms\061d59e8cf44d68f1e0e921e3a128d23\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
116            </intent-filter>
117        </receiver>
118
119        <service
119-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
120            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
120-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
121            android:exported="false" >
121-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
122            <meta-data
122-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
123                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
123-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
124                android:value="cct" />
124-->[com.google.android.datatransport:transport-backend-cct:2.3.3] D:\data\AndroidGradle\caches\8.11.1\transforms\903628ef208522e780082d8f4fbacbe9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
125        </service>
126        <service
126-->[com.google.android.datatransport:transport-runtime:2.2.6] D:\data\AndroidGradle\caches\8.11.1\transforms\0d24754bde74f3db11670c6cc7aecf02\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
127            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
127-->[com.google.android.datatransport:transport-runtime:2.2.6] D:\data\AndroidGradle\caches\8.11.1\transforms\0d24754bde74f3db11670c6cc7aecf02\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
128            android:exported="false"
128-->[com.google.android.datatransport:transport-runtime:2.2.6] D:\data\AndroidGradle\caches\8.11.1\transforms\0d24754bde74f3db11670c6cc7aecf02\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
129            android:permission="android.permission.BIND_JOB_SERVICE" >
129-->[com.google.android.datatransport:transport-runtime:2.2.6] D:\data\AndroidGradle\caches\8.11.1\transforms\0d24754bde74f3db11670c6cc7aecf02\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
130        </service>
131
132        <receiver
132-->[com.google.android.datatransport:transport-runtime:2.2.6] D:\data\AndroidGradle\caches\8.11.1\transforms\0d24754bde74f3db11670c6cc7aecf02\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
133            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
133-->[com.google.android.datatransport:transport-runtime:2.2.6] D:\data\AndroidGradle\caches\8.11.1\transforms\0d24754bde74f3db11670c6cc7aecf02\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
134            android:exported="false" />
134-->[com.google.android.datatransport:transport-runtime:2.2.6] D:\data\AndroidGradle\caches\8.11.1\transforms\0d24754bde74f3db11670c6cc7aecf02\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
135    </application>
136
137</manifest>
