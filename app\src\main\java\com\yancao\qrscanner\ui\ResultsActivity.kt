package com.yancao.qrscanner.ui

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.yancao.qrscanner.camera.QRCodeAnalyzer
import com.yancao.qrscanner.databinding.ActivityResultsBinding
import com.yancao.qrscanner.utils.BitmapSizeReduce
import com.yancao.qrscanner.utils.BroadCastUtils
import com.yancao.qrscanner.utils.DisplayUtils
import com.yancao.qrscanner.utils.ImageHolder

class ResultsActivity : AppCompatActivity() {
    private lateinit var binding: ActivityResultsBinding
    private val qrCodeAnalyzer = QRCodeAnalyzer()

    private val intent = Intent(BroadCastUtils.BROADCAST_ACTION).apply {
        putExtra(BroadCastUtils.BROADCAST_CODE_TYPE, "dataType")
        putExtra(BroadCastUtils.BROADCAST_CODE_TYPE_NAME,"codeTypeName")
        putExtra(BroadCastUtils.BROADCAST_DATA_LABEL,"barcode_string")
        putExtra(BroadCastUtils.BROADCAST_RAW_DATA,"rawData")
        putExtra(BroadCastUtils.BROADCAST_SWITCH,"1")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityResultsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        //获取屏幕可用像素大小，即宽度和高度。
        val userScreenSize = DisplayUtils.getAppUsableScreenSize(this)
        val uWidth = userScreenSize.x
        val uHeight = userScreenSize.y

        // 从临时持有者获取图像并显示
        ImageHolder.bitmap?.let { bitmap ->
            //缩小图像，并展示在页面中
            val scaledBitmap = BitmapSizeReduce.resizeBitmap(bitmap, uWidth, uHeight)
            binding.ivCapturedImage.setImageBitmap(scaledBitmap)

            //分析
            qrCodeAnalyzer.analyze(
                scaledBitmap,
                onSuccess = { qrCodes ->
                    if (qrCodes.isEmpty()) {
                        showToast("在图像中未找到任何二维码。")
                    } else {
                        Log.d("QRCodeResult", "成功识别到 ${qrCodes.size} 个二维码:")
                        //这里barcode.rawValue就是二维码的内容，需要传递给广播模块
                        val resultsText = qrCodes.mapIndexed { index, barcode ->
                            "二维码 ${index + 1}: ${barcode.rawValue}"
                        }.joinToString("\n")
                        binding.resultsDisplay.text = resultsText

                        // 修改广播内容，将扫描结果传递出去
                        val broadcastIntent = Intent(BroadCastUtils.BROADCAST_ACTION).apply {
                            putExtra(BroadCastUtils.BROADCAST_CODE_TYPE, "QR_CODE")
                            putExtra(BroadCastUtils.BROADCAST_CODE_TYPE_NAME, "QR_CODE")
                            putExtra(BroadCastUtils.BROADCAST_DATA_LABEL, "scan_result")
                            putExtra(BroadCastUtils.BROADCAST_RAW_DATA, qrCodes.firstOrNull()?.rawValue ?: "")
                            putExtra(BroadCastUtils.BROADCAST_SWITCH, "1")
                        }
                        sendBroadcast(broadcastIntent)

                        // 如果是从APP B调用的，延迟一小段时间后返回APP B
                        val fromExternal = intent.getBooleanExtra("FROM_EXTERNAL_APP", false)
                        if (fromExternal) {
                            Handler(Looper.getMainLooper()).postDelayed({
                                finish()
                            }, 500) // 延迟500毫秒后返回
                        }
                    }
                },
                onError = { exception ->
                    showToast("识别失败: ${exception.message}")
                }
            )
        } ?: showToast("无法加载图像")
    }

    // 将 UI 操作封装成一个辅助函数
    private fun showToast(message: String, isLong: Boolean = false) {
        Toast.makeText(this, message, if (isLong) Toast.LENGTH_LONG else Toast.LENGTH_SHORT).show()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清除临时持有的图像，防止内存泄漏
        ImageHolder.bitmap = null
    }
}