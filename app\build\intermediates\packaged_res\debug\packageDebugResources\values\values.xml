<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="button_accent">#4CAF50</color>
    <color name="button_primary">#2196F3</color>
    <color name="button_secondary">#FF9800</color>
    <color name="button_tertiary">#9C27B0</color>
    <color name="exposure_bright">#FFE66D</color>
    <color name="exposure_control_bg">#E0000000</color>
    <color name="exposure_dark">#FF6B6B</color>
    <color name="exposure_normal">#4ECDC4</color>
    <color name="semi_transparent_black">#80000000</color>
    <color name="text_primary_light">#FFFFFF</color>
    <color name="text_secondary_light">#B3FFFFFF</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">QRscanner</string>
    <string name="decrease_exposure">减少曝光</string>
    <string name="exposure_control">曝光控制</string>
    <string name="exposure_decreased">曝光已减少</string>
    <string name="exposure_increased">曝光已增加</string>
    <string name="exposure_max_reached">已达到最大曝光值</string>
    <string name="exposure_min_reached">已达到最小曝光值</string>
    <string name="exposure_not_supported">设备不支持曝光控制</string>
    <string name="exposure_optimized">已应用扫码曝光优化</string>
    <string name="exposure_reset">曝光已重置</string>
    <string name="exposure_value">曝光: %1$.1f EV</string>
    <string name="hide_detailed_control">隐藏详细控制</string>
    <string name="increase_exposure">增加曝光</string>
    <string name="low_light_mode">低光模式</string>
    <string name="optimize_for_scanning">扫码优化</string>
    <string name="reset_exposure">重置曝光</string>
    <string name="show_detailed_control">显示详细控制</string>
    <style name="Base.Theme.QRscanner" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="ExposureControlButton">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
    <style name="ExposureControlText">
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:textSize">12sp</item>
    </style>
    <style name="ExposureControlTitle" parent="ExposureControlText">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="Theme.QRscanner" parent="Base.Theme.QRscanner"/>
</resources>